package affiliate

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// SubscriptionInterface defines the interface for NATS subscriptions
type SubscriptionInterface interface {
	IsValid() bool
	Unsubscribe() error
}

// SubscriptionWrapper wraps nats.Subscription to implement SubscriptionInterface
type SubscriptionWrapper struct {
	*nats.Subscription
}

func (sw *SubscriptionWrapper) IsValid() bool {
	return sw.Subscription != nil && sw.Subscription.IsValid()
}

func (sw *SubscriptionWrapper) Unsubscribe() error {
	if sw.Subscription != nil {
		return sw.Subscription.Unsubscribe()
	}
	return nil
}

// AffiliateSubscriberService handles NATS subscription for affiliate transactions and SOL price updates
type AffiliateSubscriberService struct {
	natsClient       natsClient.Subscriber
	affiliateService AffiliateServiceInterface
	subscriptions    map[string]SubscriptionInterface // Track active subscriptions
	mu               sync.RWMutex                     // Protect subscriptions map
}

// AffiliateServiceInterface defines the interface for affiliate service operations
type AffiliateServiceInterface interface {
	ProcessAffiliateTransaction(ctx context.Context, txEvent *natsClient.AffiliateTxEvent) error
	ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsClient.SolPriceEvent) error
}

// NewAffiliateSubscriberService creates a new affiliate subscriber service
func NewAffiliateSubscriberService(natsClient natsClient.Subscriber, affiliateService AffiliateServiceInterface) *AffiliateSubscriberService {
	return &AffiliateSubscriberService{
		natsClient:       natsClient,
		affiliateService: affiliateService,
		subscriptions:    make(map[string]SubscriptionInterface),
	}
}

// Start begins listening to NATS streams for affiliate transactions and SOL price updates
func (s *AffiliateSubscriberService) Start(ctx context.Context) error {
	global.GVA_LOG.Info("Starting AffiliateSubscriberService")

	// Create or ensure the affiliate stream exists
	_, err := s.natsClient.AddStream(&nats.StreamConfig{
		Name:     natsClient.AffiliateStream,
		Subjects: []string{natsClient.AffiliateTxSubject, natsClient.SolPriceSubject},
		Storage:  nats.FileStorage,
	})
	if err != nil {
		return fmt.Errorf("failed to create affiliate stream: %w", err)
	}

	// Start affiliate transaction subscriber
	if err := s.startAffiliateTxSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start affiliate transaction subscriber: %w", err)
	}

	// Start SOL price subscriber
	if err := s.startSolPriceSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start SOL price subscriber: %w", err)
	}

	global.GVA_LOG.Info("AffiliateSubscriberService started successfully")
	return nil
}

// startAffiliateTxSubscriber starts the subscriber for affiliate transactions
func (s *AffiliateSubscriberService) startAffiliateTxSubscriber(ctx context.Context) error {
	subject := natsClient.AffiliateTxSubject
	consumerName := natsClient.AffiliateTxConsumer

	global.GVA_LOG.Info("Starting affiliate transaction subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	// Check if we already have an active subscription for this subject
	s.mu.RLock()
	if existingSub, exists := s.subscriptions[subject]; exists {
		s.mu.RUnlock()
		if existingSub.IsValid() {
			global.GVA_LOG.Info("Reusing existing subscription for affiliate transactions",
				zap.String("subject", subject))
			return nil
		}
		// Remove invalid subscription
		s.mu.Lock()
		delete(s.subscriptions, subject)
		s.mu.Unlock()
	} else {
		s.mu.RUnlock()
	}

	// Try to create or reuse existing consumer
	sub, err := s.createOrReuseConsumer(ctx, subject, consumerName, func(msg *nats.Msg) {
		s.handleAffiliateTxMessage(ctx, msg)
	})
	if err != nil {
		return fmt.Errorf("failed to create affiliate transaction subscriber: %w", err)
	}

	// Store the subscription
	s.mu.Lock()
	s.subscriptions[subject] = &SubscriptionWrapper{sub}
	s.mu.Unlock()

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		s.cleanupSubscription(subject, "affiliate transaction")
	}()

	global.GVA_LOG.Info("Successfully started affiliate transaction subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	return nil
}

// createOrReuseConsumer creates a new consumer or reuses an existing one
func (s *AffiliateSubscriberService) createOrReuseConsumer(_ context.Context, subject, consumerName string, handler nats.MsgHandler) (*nats.Subscription, error) {
	streamName := natsClient.AffiliateStream

	// Check if consumer already exists
	consumerInfo, err := s.natsClient.ConsumerInfo(streamName, consumerName)
	if err == nil && consumerInfo != nil {
		global.GVA_LOG.Info("Consumer already exists, attempting to bind to it",
			zap.String("consumer", consumerName),
			zap.String("subject", subject),
			zap.String("stream", streamName))

		// Try to bind to the existing consumer
		sub, err := s.natsClient.SubscribeJS(subject, handler,
			nats.Bind(streamName, consumerName),
			nats.ManualAck(),
		)

		if err != nil {
			// If binding fails, the consumer might be in an inconsistent state
			global.GVA_LOG.Warn("Failed to bind to existing consumer, attempting to delete and recreate",
				zap.String("consumer", consumerName),
				zap.String("subject", subject),
				zap.Error(err))

			// Try to delete the problematic consumer
			if deleteErr := s.natsClient.DeleteConsumer(streamName, consumerName); deleteErr != nil {
				global.GVA_LOG.Error("Failed to delete problematic consumer",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
			} else {
				global.GVA_LOG.Info("Successfully deleted problematic consumer",
					zap.String("consumer", consumerName))
			}

			// Fall through to create a new consumer
		} else {
			global.GVA_LOG.Info("Successfully bound to existing consumer",
				zap.String("consumer", consumerName),
				zap.String("subject", subject))
			return sub, nil
		}
	}

	// Create a new consumer
	global.GVA_LOG.Info("Creating new consumer",
		zap.String("consumer", consumerName),
		zap.String("subject", subject),
		zap.String("stream", streamName))

	sub, err := s.natsClient.SubscribeJS(subject, handler,
		nats.Durable(consumerName),
		nats.ManualAck(),
		nats.BindStream(streamName),
		nats.AckWait(30*time.Second),
		nats.MaxDeliver(3), // Limit redelivery attempts
		nats.DeliverAll(),  // Start from the beginning for new consumers
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create consumer %s for subject %s: %w", consumerName, subject, err)
	}

	global.GVA_LOG.Info("Successfully created new consumer",
		zap.String("consumer", consumerName),
		zap.String("subject", subject))

	return sub, nil
}

// cleanupSubscription safely cleans up a subscription
func (s *AffiliateSubscriberService) cleanupSubscription(subject, description string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if sub, exists := s.subscriptions[subject]; exists {
		global.GVA_LOG.Info("Unsubscribing from subject",
			zap.String("subject", subject),
			zap.String("description", description))

		if err := sub.Unsubscribe(); err != nil {
			global.GVA_LOG.Error("Failed to unsubscribe from subject",
				zap.String("subject", subject),
				zap.String("description", description),
				zap.Error(err))
		}

		delete(s.subscriptions, subject)
	}
}

// startSolPriceSubscriber starts the subscriber for SOL price updates
func (s *AffiliateSubscriberService) startSolPriceSubscriber(ctx context.Context) error {
	subject := natsClient.SolPriceSubject
	consumerName := natsClient.SolPriceConsumer

	global.GVA_LOG.Info("Starting SOL price subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	// Check if we already have an active subscription for this subject
	s.mu.RLock()
	if existingSub, exists := s.subscriptions[subject]; exists {
		s.mu.RUnlock()
		if existingSub.IsValid() {
			global.GVA_LOG.Info("Reusing existing subscription for SOL price updates",
				zap.String("subject", subject))
			return nil
		}
		// Remove invalid subscription
		s.mu.Lock()
		delete(s.subscriptions, subject)
		s.mu.Unlock()
	} else {
		s.mu.RUnlock()
	}

	// Try to create or reuse existing consumer
	sub, err := s.createOrReuseConsumer(ctx, subject, consumerName, func(msg *nats.Msg) {
		s.handleSolPriceMessage(ctx, msg)
	})
	if err != nil {
		return fmt.Errorf("failed to create SOL price subscriber: %w", err)
	}

	// Store the subscription
	s.mu.Lock()
	s.subscriptions[subject] = &SubscriptionWrapper{sub}
	s.mu.Unlock()

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		s.cleanupSubscription(subject, "SOL price")
	}()

	global.GVA_LOG.Info("Successfully started SOL price subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	return nil
}

// handleAffiliateTxMessage processes affiliate transaction messages from NATS
func (s *AffiliateSubscriberService) handleAffiliateTxMessage(ctx context.Context, msg *nats.Msg) {
	// Log raw message for debugging
	dataPreview := string(msg.Data)
	if len(dataPreview) > 500 {
		dataPreview = dataPreview[:500] + "..."
	}
	global.GVA_LOG.Debug("Received raw affiliate transaction message",
		zap.String("subject", msg.Subject),
		zap.Int("data_size", len(msg.Data)),
		zap.String("data_preview", dataPreview))

	var wrapper natsClient.AffiliateTxEventWrapper
	if err := json.Unmarshal(msg.Data, &wrapper); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal affiliate transaction message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	// Log the number of items received
	global.GVA_LOG.Info("Received affiliate transaction batch",
		zap.Int("item_count", len(wrapper.Items)))

	// Process each transaction in the items array
	for i, txEvent := range wrapper.Items {
		global.GVA_LOG.Info("Processing affiliate transaction",
			zap.Int("item_index", i),
			zap.String("order_id", txEvent.ID.String()),
			zap.String("user_id", txEvent.UserId),
			zap.String("wallet_address", txEvent.UserAddress),
			zap.String("transaction_type", string(txEvent.TransactionType)),
			zap.String("status", string(txEvent.Status)),
			zap.String("base_symbol", txEvent.BaseSymbol),
			zap.String("quote_symbol", txEvent.QuoteSymbol))

		// Skip processing if essential fields are missing
		if txEvent.UserId == "" {
			global.GVA_LOG.Warn("Skipping transaction with empty user_id",
				zap.String("order_id", txEvent.ID.String()))
			continue
		}

		// Process the transaction
		if err := s.affiliateService.ProcessAffiliateTransaction(ctx, &txEvent); err != nil {
			global.GVA_LOG.Error("Failed to process affiliate transaction",
				zap.Error(err),
				zap.String("order_id", txEvent.ID.String()),
				zap.String("user_id", txEvent.UserId))
			// Continue processing other transactions in the batch
			continue
		}

		global.GVA_LOG.Debug("Successfully processed affiliate transaction",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("user_id", txEvent.UserId))
	}

	// Acknowledge the message
	msg.Ack()
}

// handleSolPriceMessage processes SOL price update messages from NATS
func (s *AffiliateSubscriberService) handleSolPriceMessage(ctx context.Context, msg *nats.Msg) {
	var priceEvent natsClient.SolPriceEvent
	if err := json.Unmarshal(msg.Data, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal SOL price message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	global.GVA_LOG.Debug("Received SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	// Process the price update
	if err := s.affiliateService.ProcessSolPriceUpdate(ctx, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to process SOL price update",
			zap.Error(err),
			zap.String("symbol", priceEvent.GetSymbol()))
		// Don't ack the message so it will be redelivered
		return
	}

	// Acknowledge the message
	msg.Ack()
}

// Stop gracefully stops all subscriptions
func (s *AffiliateSubscriberService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	global.GVA_LOG.Info("Stopping AffiliateSubscriberService",
		zap.Int("active_subscriptions", len(s.subscriptions)))

	for subject, sub := range s.subscriptions {
		if sub != nil && sub.IsValid() {
			global.GVA_LOG.Info("Unsubscribing from subject", zap.String("subject", subject))
			if err := sub.Unsubscribe(); err != nil {
				global.GVA_LOG.Error("Failed to unsubscribe from subject",
					zap.String("subject", subject),
					zap.Error(err))
			}
		}
	}

	// Clear all subscriptions
	s.subscriptions = make(map[string]SubscriptionInterface)
	global.GVA_LOG.Info("AffiliateSubscriberService stopped successfully")
}

// HealthCheck checks the health of all subscriptions and attempts recovery if needed
func (s *AffiliateSubscriberService) HealthCheck(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var errors []string
	healthyCount := 0
	totalCount := len(s.subscriptions)

	for subject, sub := range s.subscriptions {
		if sub == nil || !sub.IsValid() {
			errors = append(errors, fmt.Sprintf("subscription for %s is invalid", subject))
			global.GVA_LOG.Warn("Found invalid subscription during health check",
				zap.String("subject", subject))
		} else {
			healthyCount++
		}
	}

	global.GVA_LOG.Info("Subscription health check completed",
		zap.Int("healthy", healthyCount),
		zap.Int("total", totalCount),
		zap.Int("errors", len(errors)))

	if len(errors) > 0 {
		return fmt.Errorf("subscription health check failed: %s", strings.Join(errors, "; "))
	}

	return nil
}

// GetSubscriptionStatus returns the status of all subscriptions
func (s *AffiliateSubscriberService) GetSubscriptionStatus() map[string]bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	status := make(map[string]bool)
	for subject, sub := range s.subscriptions {
		status[subject] = sub != nil && sub.IsValid()
	}

	return status
}
