package affiliate

import (
	"context"
	"testing"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// MockNATSClient is a mock implementation of the Subscriber interface
type MockNATSClient struct {
	mock.Mock
}

func (m *MockNATSClient) Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error) {
	args := m.Called(subject, handler)
	return args.Get(0).(*nats.Subscription), args.Error(1)
}

func (m *MockNATSClient) SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error) {
	args := m.Called(subject, handler, opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	// Create a real nats.Subscription for testing
	return &nats.Subscription{}, args.Error(1)
}

func (m *MockNATSClient) AddStream(cfg *nats.StreamConfig) (*nats.StreamInfo, error) {
	args := m.Called(cfg)
	return args.Get(0).(*nats.StreamInfo), args.Error(1)
}

func (m *MockNATSClient) ConsumerInfo(stream, consumer string) (*nats.ConsumerInfo, error) {
	args := m.Called(stream, consumer)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*nats.ConsumerInfo), args.Error(1)
}

func (m *MockNATSClient) DeleteConsumer(stream, consumer string) error {
	args := m.Called(stream, consumer)
	return args.Error(0)
}

// MockAffiliateService is a mock implementation of AffiliateServiceInterface
type MockAffiliateService struct {
	mock.Mock
}

func (m *MockAffiliateService) ProcessAffiliateTransaction(ctx context.Context, txEvent *natsClient.AffiliateTxEvent) error {
	args := m.Called(ctx, txEvent)
	return args.Error(0)
}

func (m *MockAffiliateService) ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsClient.SolPriceEvent) error {
	args := m.Called(ctx, priceEvent)
	return args.Error(0)
}

// MockSubscription is a mock implementation of nats.Subscription
type MockSubscription struct {
	mock.Mock
	valid bool
}

func (m *MockSubscription) IsValid() bool {
	return m.valid
}

func (m *MockSubscription) Unsubscribe() error {
	args := m.Called()
	return args.Error(0)
}

// Implement other required methods from nats.Subscription interface
func (m *MockSubscription) Subject() string                                  { return "mock.subject" }
func (m *MockSubscription) Queue() string                                    { return "" }
func (m *MockSubscription) Type() nats.SubscriptionType                      { return nats.PullSubscription }
func (m *MockSubscription) NextMsg(timeout time.Duration) (*nats.Msg, error) { return nil, nil }
func (m *MockSubscription) NextMsgWithContext(ctx context.Context) (*nats.Msg, error) {
	return nil, nil
}
func (m *MockSubscription) ChanQueueSubscribe(subject, queue string, ch chan *nats.Msg) (*nats.Subscription, error) {
	return nil, nil
}
func (m *MockSubscription) AutoUnsubscribe(max int) error                   { return nil }
func (m *MockSubscription) MaxPending() (int, int, error)                   { return 0, 0, nil }
func (m *MockSubscription) ClearMaxPending() error                          { return nil }
func (m *MockSubscription) Pending() (int, int, error)                      { return 0, 0, nil }
func (m *MockSubscription) PendingLimits() (int, int, error)                { return 0, 0, nil }
func (m *MockSubscription) SetPendingLimits(msgLimit, bytesLimit int) error { return nil }
func (m *MockSubscription) Delivered() (int64, error)                       { return 0, nil }
func (m *MockSubscription) Dropped() (int, error)                           { return 0, nil }

func TestNewAffiliateSubscriberService(t *testing.T) {
	mockNATS := &MockNATSClient{}
	mockAffiliate := &MockAffiliateService{}

	service := NewAffiliateSubscriberService(mockNATS, mockAffiliate)

	assert.NotNil(t, service)
	assert.Equal(t, mockNATS, service.natsClient)
	assert.Equal(t, mockAffiliate, service.affiliateService)
	assert.NotNil(t, service.subscriptions)
	assert.Equal(t, 0, len(service.subscriptions))
}

func TestAffiliateSubscriberService_Start(t *testing.T) {
	// Setup logger for testing
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	mockNATS := &MockNATSClient{}
	mockAffiliate := &MockAffiliateService{}

	service := NewAffiliateSubscriberService(mockNATS, mockAffiliate)

	// Mock stream creation
	mockNATS.On("AddStream", mock.AnythingOfType("*nats.StreamConfig")).Return(&nats.StreamInfo{}, nil)

	// Mock consumer info calls (consumers don't exist)
	mockNATS.On("ConsumerInfo", natsClient.AffiliateStream, natsClient.AffiliateTxConsumer).Return(nil, nats.ErrConsumerNotFound)
	mockNATS.On("ConsumerInfo", natsClient.AffiliateStream, natsClient.SolPriceConsumer).Return(nil, nats.ErrConsumerNotFound)

	// Mock subscription creation
	mockNATS.On("SubscribeJS", natsClient.AffiliateTxSubject, mock.AnythingOfType("nats.MsgHandler"), mock.Anything).Return(&nats.Subscription{}, nil)
	mockNATS.On("SubscribeJS", natsClient.SolPriceSubject, mock.AnythingOfType("nats.MsgHandler"), mock.Anything).Return(&nats.Subscription{}, nil)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := service.Start(ctx)
	assert.NoError(t, err)

	// Verify subscriptions were created
	assert.Equal(t, 2, len(service.subscriptions))

	mockNATS.AssertExpectations(t)
}

func TestAffiliateSubscriberService_HealthCheck(t *testing.T) {
	mockNATS := &MockNATSClient{}
	mockAffiliate := &MockAffiliateService{}
	service := NewAffiliateSubscriberService(mockNATS, mockAffiliate)

	// Add a valid subscription
	validSub := &MockSubscription{valid: true}
	service.subscriptions["test.subject.1"] = validSub

	// Add an invalid subscription
	invalidSub := &MockSubscription{valid: false}
	service.subscriptions["test.subject.2"] = invalidSub

	ctx := context.Background()
	err := service.HealthCheck(ctx)

	// Should return error because one subscription is invalid
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "test.subject.2")
}

func TestAffiliateSubscriberService_GetSubscriptionStatus(t *testing.T) {
	mockNATS := &MockNATSClient{}
	mockAffiliate := &MockAffiliateService{}
	service := NewAffiliateSubscriberService(mockNATS, mockAffiliate)

	// Add subscriptions
	validSub := &MockSubscription{valid: true}
	invalidSub := &MockSubscription{valid: false}
	service.subscriptions["valid.subject"] = validSub
	service.subscriptions["invalid.subject"] = invalidSub

	status := service.GetSubscriptionStatus()

	assert.Equal(t, 2, len(status))
	assert.True(t, status["valid.subject"])
	assert.False(t, status["invalid.subject"])
}

func TestAffiliateSubscriberService_Stop(t *testing.T) {
	mockNATS := &MockNATSClient{}
	mockAffiliate := &MockAffiliateService{}
	service := NewAffiliateSubscriberService(mockNATS, mockAffiliate)

	// Add mock subscriptions
	mockSub1 := &MockSubscription{valid: true}
	mockSub2 := &MockSubscription{valid: true}
	mockSub1.On("Unsubscribe").Return(nil)
	mockSub2.On("Unsubscribe").Return(nil)

	service.subscriptions["subject1"] = mockSub1
	service.subscriptions["subject2"] = mockSub2

	service.Stop()

	// Verify all subscriptions were cleaned up
	assert.Equal(t, 0, len(service.subscriptions))
	mockSub1.AssertExpectations(t)
	mockSub2.AssertExpectations(t)
}
