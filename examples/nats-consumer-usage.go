package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
)

// Example usage of the improved NATS consumer management
func main() {
	// Initialize logger
	logger, err := zap.NewDevelopment()
	if err != nil {
		log.Fatal("Failed to initialize logger:", err)
	}
	global.GVA_LOG = logger

	// NATS configuration
	natsConfig := config.Nats{
		URL:    "nats://localhost:4222",
		User:   "user",
		Pass:   "pass",
		UseTLS: false,
		Token:  "",
	}

	// Initialize NATS client
	natsClient := nats.InitNatsJetStream(natsConfig)
	defer natsClient.Close()

	// Create affiliate service (mock implementation for example)
	affiliateService := &MockAffiliateService{}

	// Create subscriber service with improved consumer management
	subscriberService := affiliate.NewAffiliateSubscriberService(natsClient, affiliateService)

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start the subscriber service
	logger.Info("Starting affiliate subscriber service...")
	if err := subscriberService.Start(ctx); err != nil {
		logger.Fatal("Failed to start subscriber service", zap.Error(err))
	}

	// Optional: Start health monitoring
	go startHealthMonitoring(ctx, subscriberService, logger)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	logger.Info("Service started successfully. Press Ctrl+C to stop...")
	<-sigChan

	logger.Info("Shutting down service...")
	cancel()

	// Graceful shutdown
	subscriberService.Stop()
	logger.Info("Service stopped successfully")
}

// startHealthMonitoring demonstrates health monitoring of subscriptions
func startHealthMonitoring(ctx context.Context, service *affiliate.AffiliateSubscriberService, logger *zap.Logger) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Perform health check
			if err := service.HealthCheck(ctx); err != nil {
				logger.Warn("Subscription health check failed", zap.Error(err))
			} else {
				logger.Info("All subscriptions are healthy")
			}

			// Get detailed status
			status := service.GetSubscriptionStatus()
			for subject, isHealthy := range status {
				logger.Info("Subscription status",
					zap.String("subject", subject),
					zap.Bool("healthy", isHealthy))
			}

		case <-ctx.Done():
			logger.Info("Health monitoring stopped")
			return
		}
	}
}

// MockAffiliateService is a mock implementation for demonstration
type MockAffiliateService struct{}

func (m *MockAffiliateService) ProcessAffiliateTransaction(ctx context.Context, txEvent *nats.AffiliateTxEvent) error {
	global.GVA_LOG.Info("Processing affiliate transaction",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("user_id", txEvent.UserId))
	return nil
}

func (m *MockAffiliateService) ProcessSolPriceUpdate(ctx context.Context, priceEvent *nats.SolPriceEvent) error {
	global.GVA_LOG.Info("Processing SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()))
	return nil
}
