# NATS Consumer Management Best Practices

## Overview

This document outlines the best practices implemented in the xbit-agent project for managing NATS JetStream consumers to avoid common issues like "consumer is already bound to a subscription" errors.

## Problem Statement

The original implementation had issues with consumer management:
- Creating new consumers with unique names when binding failed
- No proper cleanup of existing consumers
- No reuse of existing consumers
- Potential memory leaks from abandoned consumers

## Solution Implementation

### 1. Consumer Reuse Strategy

Instead of creating new consumers with unique names, the updated implementation:

1. **Checks for existing consumers** using `ConsumerInfo()`
2. **Attempts to bind** to existing consumers first
3. **Deletes problematic consumers** if binding fails
4. **Creates new consumers** only when necessary

### 2. Subscription Tracking

The service now maintains a map of active subscriptions:
```go
type AffiliateSubscriberService struct {
    subscriptions map[string]*nats.Subscription
    mu           sync.RWMutex
}
```

### 3. Graceful Cleanup

- Proper unsubscription on context cancellation
- Cleanup of invalid subscriptions
- Complete shutdown method for service termination

### 4. Health Monitoring

- Health check method to verify subscription validity
- Status reporting for monitoring
- Automatic detection of invalid subscriptions

## Key Methods

### `createOrReuseConsumer()`
- Checks if consumer exists
- Attempts to bind to existing consumer
- Deletes and recreates if binding fails
- Creates new consumer with proper configuration

### `cleanupSubscription()`
- Thread-safe subscription cleanup
- Proper unsubscription and resource cleanup
- Removal from tracking map

### `HealthCheck()`
- Validates all active subscriptions
- Reports health status
- Identifies problematic subscriptions

## Configuration Best Practices

### Consumer Configuration
```go
nats.Durable(consumerName),           // Persistent consumer
nats.ManualAck(),                     // Manual acknowledgment
nats.BindStream(streamName),          // Bind to specific stream
nats.AckWait(30*time.Second),         // Acknowledgment timeout
nats.MaxDeliver(3),                   // Limit redelivery attempts
nats.DeliverAll(),                    // Start from beginning for new consumers
```

### Error Handling
- Specific error checking for binding issues
- Graceful degradation when consumers are problematic
- Comprehensive logging for debugging

## Benefits

1. **No Duplicate Consumers**: Reuses existing consumers instead of creating new ones
2. **Resource Efficiency**: Proper cleanup prevents resource leaks
3. **Reliability**: Health monitoring and automatic recovery
4. **Observability**: Comprehensive logging and status reporting
5. **Thread Safety**: Proper synchronization for concurrent access

## Usage Example

```go
// Create service with proper initialization
service := NewAffiliateSubscriberService(natsClient, affiliateService)

// Start subscriptions
err := service.Start(ctx)
if err != nil {
    log.Fatal("Failed to start subscriber service:", err)
}

// Health monitoring (optional)
go func() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            if err := service.HealthCheck(ctx); err != nil {
                log.Warn("Subscription health check failed:", err)
            }
        case <-ctx.Done():
            return
        }
    }
}()

// Graceful shutdown
defer service.Stop()
```

## Monitoring

The service provides methods for monitoring:
- `HealthCheck()`: Validates subscription health
- `GetSubscriptionStatus()`: Returns status map of all subscriptions
- Comprehensive logging throughout the lifecycle

## Migration Notes

When migrating from the old implementation:
1. Existing consumers will be automatically detected and reused
2. No manual cleanup of old consumers is required
3. The service will handle problematic consumers automatically
4. Monitor logs for any binding issues during initial migration
