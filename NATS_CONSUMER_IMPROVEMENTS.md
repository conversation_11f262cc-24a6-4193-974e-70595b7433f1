# NATS Consumer Management Improvements

## Tóm tắt thay đổi

Đã cập nhật cơ chế xử lý NATS JetStream consumers để tránh lỗi "consumer is already bound to a subscription" và implement các best practices cho việc quản lý consumer.

## Vấn đề trước đây

1. **Tạo consumer trùng lặp**: Khi gặp lỗi binding, code cũ tạo consumer mới với tên unique
2. **Không tái sử dụng consumer**: Không kiểm tra và tái sử dụng consumer đã tồn tại
3. **Quản lý subscription kém**: Không track và cleanup subscription đúng cách
4. **Thiếu monitoring**: Không có cách kiểm tra health của subscriptions

## Giải pháp đã implement

### 1. Consumer Reuse Strategy
- **Kiểm tra consumer tồn tại** trước khi tạo mới
- **Bind vào consumer hiện có** thay vì tạo mới
- **Xóa consumer problematic** nếu binding thất bại
- **Tạo consumer mới** chỉ khi cần thiết

### 2. Subscription Tracking
```go
type AffiliateSubscriberService struct {
    subscriptions map[string]SubscriptionInterface
    mu           sync.RWMutex
}
```

### 3. Interface Abstraction
```go
type SubscriptionInterface interface {
    IsValid() bool
    Unsubscribe() error
}
```

### 4. Enhanced NATS Client
Thêm methods cho consumer management:
```go
type Subscriber interface {
    // ... existing methods
    ConsumerInfo(stream, consumer string) (*nats.ConsumerInfo, error)
    DeleteConsumer(stream, consumer string) error
}
```

## Các tính năng mới

### 1. Smart Consumer Management
- `createOrReuseConsumer()`: Tự động kiểm tra và tái sử dụng consumer
- Xử lý consumer problematic bằng cách xóa và tạo lại
- Logging chi tiết cho debugging

### 2. Health Monitoring
- `HealthCheck()`: Kiểm tra tình trạng tất cả subscriptions
- `GetSubscriptionStatus()`: Trả về status map của subscriptions
- Tự động phát hiện subscription không hợp lệ

### 3. Graceful Shutdown
- `Stop()`: Cleanup tất cả subscriptions an toàn
- Thread-safe operations với mutex
- Proper resource cleanup

### 4. Comprehensive Testing
- Unit tests với mock implementations
- Test coverage cho tất cả scenarios chính
- Health check và status reporting tests

## Cấu hình Consumer Best Practices

```go
nats.Durable(consumerName),           // Persistent consumer
nats.ManualAck(),                     // Manual acknowledgment
nats.BindStream(streamName),          // Bind to specific stream
nats.AckWait(30*time.Second),         // Acknowledgment timeout
nats.MaxDeliver(3),                   // Limit redelivery attempts
nats.DeliverAll(),                    // Start from beginning for new consumers
```

## Cách sử dụng

### Basic Usage
```go
// Tạo service
service := affiliate.NewAffiliateSubscriberService(natsClient, affiliateService)

// Start subscriptions
err := service.Start(ctx)
if err != nil {
    log.Fatal("Failed to start:", err)
}

// Graceful shutdown
defer service.Stop()
```

### Health Monitoring
```go
// Kiểm tra health
if err := service.HealthCheck(ctx); err != nil {
    log.Warn("Health check failed:", err)
}

// Lấy status chi tiết
status := service.GetSubscriptionStatus()
for subject, isHealthy := range status {
    log.Info("Subject:", subject, "Healthy:", isHealthy)
}
```

## Lợi ích

1. **Không tạo consumer trùng lặp**: Tái sử dụng consumer hiện có
2. **Resource efficiency**: Cleanup đúng cách, không memory leak
3. **Reliability**: Health monitoring và auto recovery
4. **Observability**: Logging chi tiết và status reporting
5. **Thread safety**: Synchronization đúng cách cho concurrent access
6. **Testability**: Comprehensive test coverage

## Files đã thay đổi

- `internal/service/affiliate/affiliate_subscriber.go`: Core implementation
- `internal/nats/subscriber.go`: Interface mở rộng
- `internal/nats/nats.go`: Consumer management methods
- `internal/service/affiliate/affiliate_subscriber_test.go`: Unit tests
- `docs/nats-consumer-best-practices.md`: Documentation
- `examples/nats-consumer-usage.go`: Usage example

## Migration Notes

- Existing consumers sẽ được tự động detect và reuse
- Không cần manual cleanup consumers cũ
- Service sẽ tự động handle problematic consumers
- Monitor logs để check binding issues trong quá trình migration

## Monitoring và Debugging

Service cung cấp comprehensive logging:
- Consumer creation/binding attempts
- Health check results
- Subscription status changes
- Error handling và recovery actions

Sử dụng log level `INFO` để monitor normal operations và `DEBUG` cho detailed troubleshooting.
